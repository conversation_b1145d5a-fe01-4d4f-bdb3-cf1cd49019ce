import mongoose, { Document, Schema } from 'mongoose';

// 定义数据模型的 Schema
export interface IQuestion extends Document {
    sketch: {
        [language: string]: string;
    };
    content: {
        [language: string]: string;
    };
    question: {
        [language: string]: string;
    };
}

const QuestionSchema = new Schema({
    sketch: {
        type: Map,
        of: String,
        required: true
    },
    content: {
        type: Map,
        of: String,
        required: true
    },
    question: {
        type: Map,
        of: String,
        required: true
    }
},{
    versionKey: false
});

const modelName = 'questions';


// 创建数据模型
export const Question = mongoose.models[modelName] || mongoose.model(modelName, QuestionSchema);