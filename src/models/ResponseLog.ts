// models/ResponseLog.ts
import mongoose, { Document, Schema, Model } from 'mongoose';

interface IResponseLog extends Document {
    requestLogId: mongoose.Schema.Types.ObjectId;
    requestBody: any;
    responseBody: any;
    responseStatus: number;
    responseTime: Date;
}

const ResponseLogSchema: Schema = new Schema({
    requestLogId: { type: Schema.Types.ObjectId, ref: 'RequestLog', required: true },
    requestBody: { type: Schema.Types.Mixed },
    responseBody: { type: Schema.Types.Mixed },
    responseStatus: { type: Number, required: true },
    responseTime: { type: Date, default: Date.now }
},{
    versionKey: false
});
const modelName = 'ResponseLog';
const ResponseLog: Model<IResponseLog> = mongoose.models[modelName] || mongoose.model<IResponseLog>(modelName, ResponseLogSchema);
export default ResponseLog;
