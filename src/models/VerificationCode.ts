// models/VerificationCode.ts
import mongoose, { Document, Schema } from 'mongoose';

export interface VerificationCodeModel extends Document {
    email: string;
    code: string;
    expiresAt: Date;
}

const verificationCodeSchema = new Schema({
    email: { type: String, required: true },
    code: { type: String, required: true },
    expiresAt: { type: Date, required: true }
},{
    versionKey: false
});

// 创建 TTL 索引，指定 expiresAt 字段，并设置过期时间为 0
verificationCodeSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

export default mongoose.model<VerificationCodeModel>('VerificationCode', verificationCodeSchema);
