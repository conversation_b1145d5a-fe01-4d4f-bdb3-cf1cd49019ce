import mongoose, { Schema, Document } from 'mongoose';
import { IVerificationResult } from 'src/models/VerificationResult';


interface IUserVerification extends Document {
    userId: string;
    verificationResult: IVerificationResult;
    amount: number;
    createdAt: Date;
}

const UserVerificationSchema: Schema = new Schema({
    userId: { type: String, required: true },
    verificationResult: { type: Schema.Types.ObjectId, ref: 'VerificationResult', required: true },
    amount: { type: Number, required: true },
    createdAt: { type: Date, default: Date.now }
},{
    versionKey: false
});

const UserVerification = mongoose.model<IUserVerification>('UserVerification', UserVerificationSchema);

export default UserVerification;
