import express from 'express';
import { getConfig, limit } from 'src/business/getConfig';
import { getProducts } from 'src/business/getProducts';
import { getPricing } from 'src/business/getPricing';
import { generateTitle } from 'src/business/generateTitle';
import { generateQuestion } from 'src/business/generateQuestion';
import errorHandler from 'src/middlewares/errorHandler';

const router = express.Router();

router.get('/getPricing', getPricing);
router.get('/getConfig', getConfig);
router.get('/getProducts', getProducts);
router.post('/generateTitle', generateTitle);
router.post('/generateQuestion', generateQuestion);
router.get('/limit', limit);
router.use(errorHandler);

export default router;