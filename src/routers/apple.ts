import express from 'express';
import appleLogin from 'src/business/appleAuth';
import verifyPurchase from 'src/business/verifyPurchase';
import handleRefund from 'src/business/refund';
import errorHandler from 'src/middlewares/errorHandler';

const router = express.Router();

router.post('/appleLogin', appleLogin);
router.post('/verifyPurchase', verifyPurchase);
router.post('/refund', handleRefund);
router.use(errorHandler);

export default router;
