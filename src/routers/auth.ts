import express from 'express';
import login  from 'src/business/auth';
import  refreshToken  from 'src/business/refreshToken';
import register from 'src/business/register';
import sendEmailCode from 'src/business/sendEmailCode';
import verifyEmailCode from 'src/business/verifyEmailCode';
import deleteAccount from 'src/business/deleteAccount';
import userBalance from 'src/business/userBalance';
import errorHandler from 'src/middlewares/errorHandler';


const router = express.Router();
router.post('/login', login);
router.post('/register', register);
router.post('/sendEmailCode', sendEmailCode);
router.post('/verifyEmailCode', verifyEmailCode);
router.get('/userBalance', userBalance);
router.delete('/deleteAccount', deleteAccount);
router.post('/refreshToken', refreshToken);
router.use(errorHandler);
export default router;
