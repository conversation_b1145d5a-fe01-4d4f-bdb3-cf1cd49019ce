<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Deletion Confirmation</title>
</head>
<body>
    <h1>Confirm Data Deletion</h1>
    <form id="confirmationForm">
        <label for="confirmationCode">Enter Confirmation Code:</label>
        <input type="text" id="confirmationCode" name="confirmationCode" required>
        <button type="submit">Submit</button>
    </form>
    <p id="result"></p>

    <script>
        document.getElementById('confirmationForm').addEventListener('submit', async (event) => {
            event.preventDefault();
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('id');
            const confirmationCode = document.getElementById('confirmationCode').value;

            const response = await fetch(`/verify-deletion?id=${userId}&code=${confirmationCode}`);
            const result = await response.text();

            document.getElementById('result').innerText = result;
        });
    </script>
</body>
</html>
