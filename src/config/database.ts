import mongoose from 'mongoose';
import env, { ENV_MONGODB_URI } from 'src/config/env';

export const MONGODB_URI = process.env.MONGODB_URI || ENV_MONGODB_URI;
import { exec } from 'child_process';
import { existsSync, copyFileSync } from 'fs';
import { resolve } from 'path';

// 当前目录下的 mongod.conf 文件路径
const currentConfigPath = process.cwd() + '/mongod.conf';
// MongoDB 默认配置文件路径
const defaultConfigPath = '/usr/local/etc/mongod.conf';

// 检查当前操作系统是否为 macOS
const isMacOS = process.platform === 'darwin';

// 重启 MongoDB 服务
function restartMongoDB() {
    const restartCommand = isMacOS ? 'brew services restart mongodb' : 'service mongod restart';
    exec(restartCommand, (error, stdout, stderr) => {
        if (error) {
            console.error('Failed to restart MongoDB:', error.message);
            return;
        }
        // console.log('MongoDB restarted successfully');
        initiateReplicaSet(); // 重启成功后执行初始化副本集操作
    });
}

// 初始化副本集
function initiateReplicaSet() {
    const initCommand = isMacOS ? 'mongosh --eval "rs.initiate()"' : 'mongo --eval "rs.initiate()"';
    exec(initCommand, (error, stdout, stderr) => {
        if (error) {
            console.error('Failed to initiate replica set:', error.message);
            return;
        }
        // console.log('Replica set initiated successfully');
    });
}

const connectDatabase = async (): Promise<void> => {
    try {
        await mongoose.connect(MONGODB_URI);
        // console.log('Successfully connected to MongoDB.');
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        process.exit(1);
    }
};

// MongoDB 实例端口号
const ports = [27017, 27018, 27019];

// 启动 MongoDB 实例并添加到副本集
function startMongoInstance(port: number) {
    const command = `mongod --config ${currentConfigPath} --port ${port}`;
    exec(command, (error, stdout, stderr) => {
        if (error) {
            console.error(`Failed to start MongoDB instance on port ${port}: ${error.message}`);
            return;
        }
        // console.log(`MongoDB instance on port ${port} started successfully`);
    });
}

// 添加副本集成员
function addReplicaSetMember(port: number) {
    const command = `mongo --eval "rs.add('localhost:${port}')"`;
    exec(command, (error, stdout, stderr) => {
        if (error) {
            console.error(`Failed to add replica set member for port ${port}: ${error.message}`);
            return;
        }
        // console.log(`Replica set member for port ${port} added successfully`);
    });
}

const main = async () => {
    // 如果当前目录下的 mongod.conf 与默认配置文件不一致且存在，则拷贝当前目录下的配置文件到默认路径
    if (existsSync(currentConfigPath) && currentConfigPath !== defaultConfigPath) {
        copyFileSync(currentConfigPath, defaultConfigPath);
        // 执行重启 MongoDB 服务操作
        restartMongoDB();
        // console.log('MongoDB configuration file copied successfully');
    } else {
        // console.log('No need to copy MongoDB configuration file');
    }
    // initiateReplicaSet();
    ports.slice(1).forEach(port => {
        addReplicaSetMember(port);
    });
    // 启动 MongoDB 实例并初始化副本集
    ports.forEach(port => {
        startMongoInstance(port);
    });
};

// setTimeout(() => {
//     main()
// }, 5000); // 延迟 5 秒执行副本集初始化和添加成员操作

export default connectDatabase;
