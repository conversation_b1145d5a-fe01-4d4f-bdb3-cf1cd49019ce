import OpenAI from 'openai';
import { Request, Response, NextFunction } from 'express';
import { ChatCompletionMessageParam } from 'openai/resources';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';


const openai = new OpenAI();

export async function generateTitle(req: Request, res: Response, next: NextFunction) {
    if (1) {
        try {
            // Assuming body is plain text based on your existing code
            if (typeof req.body !== 'string') {
                next({ code: HttpStatusCode.BadRequest, message: 'Invalid request body, expected a string.' });
                return;
            }
            let messages = req.body;
            let newBody: Array<ChatCompletionMessageParam> = [];
            newBody.push({ role: 'user', content: req.t('generate_title_content') + messages });

            // // console.log('newBody', newBody)

            const stream = openai.beta.chat.completions.stream({
                model: 'gpt-3.5-turbo',
                stream: true,
                messages: newBody
            });

            res.header('Content-Type', 'text/plain');
            const readable = stream.toReadableStream();
            // @ts-ignore
            for await (const chunk of stream.toReadableStream()) {
                res.write(chunk);
            }
            res.end();
        } catch (e) {
            logger.error(`Error:  ${e}`);
            next(e);
        }
    } else {
        const jsonData = [
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { role: 'assistant', content: '' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '标题' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '：' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '探' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '讨' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '如' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '何' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '实' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '现' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '个' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '人' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '力' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '量' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '的' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '增' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: { content: '长' }, logprobs: null, finish_reason: null }]
            },
            {
                id: 'chatcmpl-9LhinEY3yNV6VOieMy3RFfPH9cK7f',
                object: 'chat.completion.chunk',
                created: 1714959981,
                model: 'gpt-3.5-turbo-0125',
                system_fingerprint: 'fp_a450710239',
                choices: [{ index: 0, delta: {}, logprobs: null, finish_reason: 'stop' }]
            }
        ];
        // console.log('User-Agent:', req.headers['user-agent']);
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        // jsonData 数组中的id重新随机生成
        let newId = `chatcmpl-${Math.random().toString(36).substr(2, 15)}`;
        jsonData.forEach((item,index) => {
            item.id = newId;
            if ('content' in item.choices[0].delta) {
                item.choices[0].delta.content = req.body[index]
            }
        });

        function sendChatResponse(index: number) {
            if (index < jsonData.length) {
                // // console.log('Sending response:', JSON.stringify(jsonData[index]));
                // if ios client, use `${JSON.stringify(jsonData[index])}`
                res.write(`data: ${JSON.stringify(jsonData[index])}\n\n`);
                setTimeout(() => {
                    sendChatResponse(index + 1);
                }, 100); // Delay between messages in milliseconds
            } else {
                // If all messages sent, close the connection
                res.end();
            }
        }

        // Start sending chat responses
        sendChatResponse(0);
    }
}
