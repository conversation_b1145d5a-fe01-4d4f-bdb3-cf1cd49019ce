import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import OpenAI from 'openai';
import Pricing from 'src/models/Pricing';
import UserBalance from 'src/models/Balance';
import { ChatMessageModel, Role } from 'src/models/ChatMessage';
import BalanceTransaction, { TransactionType } from 'src/models/BalanceTransaction';
import env from 'src/config/env';
import { randomUUID } from 'crypto';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';
import { encoding_for_model, TiktokenModel } from 'tiktoken';
const openai = new OpenAI({
    apiKey: '************************************************************************************',
    baseURL: 'https://api.x.ai/v1/'
});


async function chatWithOpenai(req: Request, res: Response, next: NextFunction) {
    let uuid = randomUUID();

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        // @ts-ignore
        const userId = req.token.userId; // 从 token 中获取用户 ID
        const { modelname, chatid, 'remote-recognize': remote_recognize } = req.headers;

        const modelPrice = await Pricing.findOne({ modelName: modelname ?? 'gpt-4o-mini' });
        if (!modelPrice) {
            throw new Error('internal_server_error');
        }

        const modelName = modelPrice.modelName as TiktokenModel;
        // gpt-4o 和 gpt-4o-mini 使用相同的编码
        const encoding = encoding_for_model("gpt-4o");
        

        let tokenLength = 0;
        if (remote_recognize === '1') {
            logger.debug('remote-recognize', remote_recognize);
            req.body.forEach((msg: any) => {
                if (msg.role === 'system') {
                    tokenLength += encoding.encode(msg.content).length;
                } else if (msg.role === 'user') {
                    const totalCost = msg.total_token_cost;
                    const contentCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'image_url') {
                            if (item.token_cost != calculateImageTokens(item.width, item.height, 'low')) {
                                return -1;
                            }
                            return acc + item.token_cost;
                        }
                        return acc;
                    }, 0);
                    if (totalCost !== contentCost) {
                        throw new Error('token_cost_mismatch');
                    }
                    const testCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'text') {
                            return acc + encoding.encode(item.text).length;
                        }
                        return acc;
                    }, 0);
                    tokenLength += contentCost;
                    tokenLength += testCost;
                }
            });
        } else {
            tokenLength = req.body.reduce((acc: number, msg: any) => acc + encoding.encode(msg.content).length, 0);
        }

        const consumption = parseFloat(((tokenLength / modelPrice.count) * modelPrice.inPrice).toFixed(4));
        const userBalance = await UserBalance.findOne({ userId }).session(session);

        if (!userBalance) {
            throw new Error('internal_server_error');
        }

        if (userBalance.balance < consumption) {
            logger.debug('余额不足');
            res.status(HttpStatusCode.Ok).json({ code: HttpStatusCode.BadRequest, data: null, message: req.t('no_balance_1') + ',' +  req.t('no_balance_1')});
            res.end();
            return;
        }

        const updatedBalance = await UserBalance.findOneAndUpdate({ userId, balance: userBalance.balance }, { $inc: { balance: -consumption } }, { new: true }).session(session);
        if (!updatedBalance) {
            throw new Error('internal_server_error');
        }

        const balanceTransaction = new BalanceTransaction({
            userId,
            amount: -consumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.In
        });
        await balanceTransaction.save({ session });

        await session.commitTransaction();
        session.endSession();

        // 数据清理
        const cleanedMessages = req.body.map((msg: any) => {
            const { content, role } = msg;
            if (role === 'user' && msg.content && remote_recognize === '1') {
                const cleanedContent = msg.content.map((item: any) => {
                    if (item.type === 'image_url') {
                        // 仅保留 image_url 的 detail 和 url
                        return { type: 'image_url', image_url: item.image_url };
                    }
                    return item;
                });
                return { content: cleanedContent, role };
            }
            return { content, role };
        });

        let fullResponse = '';
        let messageId = '';
        logger.debug('cleanedMessages', cleanedMessages);

        const response = await openai.chat.completions.create({
            model: "grok-2-latest",
            messages: cleanedMessages,
            frequency_penalty: 1,
            temperature: Math.random() * 0.3 + 0.7,
            presence_penalty: 1,
            response_format: { "type": "json_object" } 
        });
        messageId = response.id;

        if (response.choices[0].message.content === undefined) {
            throw new Error('internal_server_error');
        }
        fullResponse = response.choices[0].message.content!;
        // 如果fullResponse是json数据，转换为json对象
        if (fullResponse.startsWith('{')) {
            res.status(HttpStatusCode.Ok).json({ code: HttpStatusCode.Ok, data: JSON.parse(fullResponse), message: 'success' });
        } else {
            // 转换为
            res.status(HttpStatusCode.Ok).json({
                code: HttpStatusCode.Ok,
                data: {
                  level: 1,
                  description: fullResponse,
                  choices: [
                    {
                      option: 1,
                      description: req.t('internal_server_error')
                    }
                  ]
                },
                message: 'success'
              });
        }
        logger.debug('fullResponse', fullResponse);

        const responseLength = encoding.encode(fullResponse).length;
        logger.debug('responseLength', responseLength);
        const responseConsumption = parseFloat(((responseLength / modelPrice.count) * modelPrice.outPrice).toFixed(4));
        const responseUpdatedBalance = await UserBalance.findOneAndUpdate({ userId }, { $inc: { balance: -responseConsumption } }, { new: true });
        if (!responseUpdatedBalance) {
            throw new Error('internal_server_error');
        }

        const responseBalanceTransaction = new BalanceTransaction({
            userId,
            amount: -responseConsumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.Out
        });
        await responseBalanceTransaction.save();

        if (fullResponse !== '') {
            await ChatMessageModel.create({
                id: messageId,
                chatId: chatid ?? randomUUID,
                createdTime: Date.now(),
                role: Role.ASSISTANT,
                content: fullResponse,
                isComplete: true
            });
        }
        res.end();
    } catch (e) {
        logger.error('catch error:', e);
        await session.abortTransaction();
        const errorResponse = {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        // @ts-ignore
                        content: e.message || req.t('internal_server_error')
                    },
                    logprobs: null,
                    finish_reason: 'error'
                }
            ]
        };
        res.status(HttpStatusCode.Ok).json({ code: HttpStatusCode.BadRequest, data: errorResponse, message: 'success' });
        res.end();
    } finally {
        if (session.inTransaction()) {
            await session.abortTransaction();
        }
        session.endSession();
    }
}

/**
 * 计算图像的token成本
 * @param width - 图像的宽度
 * @param height - 图像的高度
 * @param detail - 图像的详细程度 ('low' 或 'high')
 * @returns 返回图像的token成本
 */
function calculateImageTokens(width: number, height: number, detail: 'low' | 'high'): number {
    // detail: low 模式下，固定成本为 85 tokens
    if (detail === 'low') {
        return 85;
    }

    // 确保图像适应2048x2048的范围
    if (width > 2048 || height > 2048) {
        const scalingFactor = Math.min(2048 / width, 2048 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 确保图像的最短边为768px长
    if (width < 768 || height < 768) {
        const scalingFactor = Math.max(768 / width, 768 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 计算512px正方形的数量
    const numTiles = Math.ceil(width / 512) * Math.ceil(height / 512);

    // detail: high 模式下，成本为170 tokens每个512px正方形，加上85 tokens的固定成本
    return numTiles * 170 + 85;
}

export default chatWithOpenai;
