import { Request, Response } from 'express';
import DataDeletionRecord from 'src/models/DataDeletionRecord';
import { logger } from './logger';

// 验证删除记录的端点
const verifyDeletionRecord = async (req: Request, res: Response) => {
    // 确保 `id` 和 `code` 被转换为字符串类型
    const id = req.query.id as string | undefined;
    const code = req.query.code as string | undefined;

    logger.debug('id:', id);
    logger.debug('code:', code);

    if (!id || !code) {
        return res.status(400).send('Invalid request');
    }

    try {
        const record = await DataDeletionRecord.findOne({
            userId: id,
            confirmationCode: code
        });

        if (record) {
            res.status(200).send('Data deletion confirmed');
        } else {
            res.status(404).send('No record found');
        }
    } catch (error) {
        console.error('Verification error:', error);
        res.status(500).send('Internal server error');
    }
};

export default verifyDeletionRecord;
