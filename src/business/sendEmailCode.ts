import { Request, Response, NextFunction } from 'express';
import nodemailer from 'nodemailer';
import VerificationCode from 'src/models/VerificationCode';
import { DateTime } from 'luxon';
import { HttpStatusCode } from 'axios';
import User from 'src/models/User';
import { logger } from './logger';

export default async function sendEmailCode(req: Request, res: Response, next: NextFunction): Promise<void> {
    const { email } = req.body;

    // 先查看数据库中是否存在该邮箱
    let user = await User.findOne({ email }).lean();
    if (user) {
        return next({ message: req.t('user_exist'), code: HttpStatusCode.BadRequest, data: null });
    }

    // 生成验证码
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // 计算验证码的过期时间（10分钟后）
    const now = DateTime.now().setZone('Asia/Shanghai');
    const expiresAt = now.plus({ minutes: 10 });

    // 保存验证码到数据库
    await VerificationCode.create({ email, code: verificationCode, expiresAt });

    const internationalDomains = [
        'gmail.com',
        'yahoo.com',
        'outlook.com',
        'hotmail.com',
        'aol.com',
        'protonmail.com',
        'icloud.com',
        'mail.com',
        'gmx.com',
        'zoho.com',
        'yandex.com',
        'me.com',
        'fastmail.com',
        'hushmail.com',
        'inbox.com'
    ];    

    // 检查邮箱域名
    const emailDomain = email.split('@')[1];
    let smtpConfig;
    let sender;

    if (internationalDomains.includes(emailDomain)) {
        smtpConfig = {
            host: 'smtpdm.aliyun.com',
            port: 465,
            secure: true, // true for 465, false for other ports
            auth: {
                user: '<EMAIL>',
                pass: '1GNgnesAn53KfOf'
            }
        };
        sender = `"${req.t('email.sender')}" <<EMAIL>>`
    } else {
        smtpConfig = {
            host: 'mail.sanva.tk',
            port: 465,
            secure: true, // true for 465, false for other ports
            auth: {
                user: '<EMAIL>',
                pass: '1GNgnesAn53Kf+Of'
            }
        };
        sender =  `"${req.t('email.sender')}" <<EMAIL>>`
    }

    // 配置nodemailer
    const transporter = nodemailer.createTransport(smtpConfig);

    const mailOptions = {
        from: sender,
        to: email,
        subject: req.t('email.subject'),
        html: `
        <div style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; text-align: center; margin: 20px;">
        <h2>${req.t('email.body')}</h2>
        <div style="font-size: 24px; padding: 10px; background-color: #F0F0F0; border-radius: 5px; display: inline-block;">
            <b style="color: #2F67F6;">${verificationCode}</b>
        </div>
        <p style="margin-top: 20px; color: #555;">${req.t('email.common_content')}</p>
        <p>${req.t('email.purpose')}</p>
        </div>
        `
    };

    transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
            next(error);
            console.error(error);
            return;
        }
        logger.debug(`Message sent: ${JSON.stringify(info)}`);
        res.status(HttpStatusCode.Ok).json({ message: req.t('verification_code_sent'), code: HttpStatusCode.Ok, data: null });
    });
};
