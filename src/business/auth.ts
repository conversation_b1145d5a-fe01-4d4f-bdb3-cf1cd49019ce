import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcrypt';
import User from 'src/models/User';
import { secretKey, tokenExpiresIn } from 'src/config/env';
import jwt from 'jsonwebtoken';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';
import i18next from 'src/config/i18nNext';

// 用户登录逻辑
export default async function login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
        const { email, password } = req.body;
        // console.log('email:', email);
        const user = await User.findOne({ email }).lean();

        if (!user) {
            return next({ message: req.t('user_not_exist'), code: HttpStatusCode.NotFound });
        }

        // console.log('password:', password);
        let userPassword = user.password;
        if (userPassword === undefined) {
            next({ message: req.t('login_error'), code: HttpStatusCode.Unauthorized });
            return;
        }
        // console.log('userPassword: ', userPassword)
        // const isPasswordValid = await bcrypt.compare(password, userPassword);
        const isPasswordValid = password === userPassword;
        if (!isPasswordValid) {
            next({ message: req.t('login_error'), code: HttpStatusCode.Unauthorized });
            return;
        }
        logger.debug('User logged in: ', user);
        const token = jwt.sign({ userId: user._id, email: user.email }, secretKey, { expiresIn: tokenExpiresIn });
        user.password = undefined;
        res.status(HttpStatusCode.Ok).json({
            message: '登录成功',
            code: HttpStatusCode.Ok,
            data: { ...user, userId: user._id, token }
        });
    } catch (error) {
        console.error(error);
        // @ts-ignore
        next({ message: `${req.t('login_fail')}: ${error.message}`, code: HttpStatusCode.InternalServerError });
    }
};

async function hashPassword(password: string): Promise<string> {
    const saltRounds = 10; // or higher based on security requirement
    try {
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        return hashedPassword;
    } catch (error) {
        logger.error(`Hashing failed: ${error}`);
        throw error;
    }
}
