import { Request, Response, NextFunction } from 'express';
import { secretKey, tokenExpiresIn } from 'src/config/env';
import jwt, { decode } from 'jsonwebtoken';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';
import User from 'src/models/User';

// 用于更新 token 的接口
export default async function refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
        // 从请求头中获取旧的 token
        const oldToken = req.headers.authorization?.split(' ')[1];
        if (!oldToken) {
            return next({ message: req.t('token_required'), code: HttpStatusCode.BadRequest });
        }
        // 验证旧的 token，并获取其 payload
        const decoded = jwt.verify(oldToken, secretKey) as jwt.JwtPayload;
        if (!await User.exists({email: decoded.email, _id: decoded.userId})) {
            return next({message: req.t('user_not_exist'), code: HttpStatusCode.BadRequest})
        }
        const now = Math.floor(Date.now() / 1000);
        const expiration = decoded.exp;
        if (!expiration) {
            return next({ message: req.t('data_not_found'), code: HttpStatusCode.BadRequest });
        }
        const newToken = jwt.sign({ userId: decoded.userId, email: decoded.email }, secretKey, { expiresIn: tokenExpiresIn });

        // 返回新的 token 给客户端
        res.status(HttpStatusCode.Ok).json({ message: 'success', data: newToken, code: HttpStatusCode.Ok });
    } catch (error: any) {
        console.error(error);
        next({ message: req.t('internal_server_error'), code: HttpStatusCode.InternalServerError });
    }
};
