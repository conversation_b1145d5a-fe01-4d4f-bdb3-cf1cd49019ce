import { Router, Request, Response, NextFunction } from 'express';
import fs from 'fs';
import OpenAI from 'openai';
import mongoose from 'mongoose';
import mime from 'mime-types';
import chatWithOpenai from 'src/business/chat';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';

const router = Router();
const openai = new OpenAI();

async function uploadAudioChat(req: Request, res: Response, next: NextFunction) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const language = req.acceptsLanguages()[0];
        if (!req.file) {
            res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
            return;
        }
        const audioFilePath = req.file.path;

        // 处理文件扩展名
        const mimeType = mime.lookup(req.file.originalname);
        if (!mimeType) {
            throw new Error('Unrecognized file format');
        }
        const extension = mime.extension(mimeType);
        if (!extension) {
            throw new Error('Unrecognized file format');
        }
        const newFilePath = `${audioFilePath}.${extension}`;
        fs.renameSync(audioFilePath, newFilePath);

        // 使用 OpenAI Whisper 模型将音频转换为文本
        const transcription = await openai.audio.transcriptions.create({
            model: 'whisper-1',
            file: fs.createReadStream(newFilePath),
            response_format: 'text',
            language
        });

        // 删除临时音频文件
        fs.unlinkSync(newFilePath);
        logger.debug('Transcription:', transcription);

        // 将转换后的文本作为消息体传递给 chatWithOpenai 接口进行处理
        req.body = [...req.body, { role: 'user', content: transcription }];
        req.headers.modelname = req.body.modelname; // 从请求头中获取模型名
        req.headers.chatid = req.body.chatid; // 从请求头中获取聊天 ID

        await chatWithOpenai(req, res, next);

        await session.commitTransaction();
        session.endSession();
    } catch (e) {
        logger.error('Audio upload error:', e);
        await session.abortTransaction();
        // @ts-ignore
        res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
    } finally {
        if (session.inTransaction()) {
            await session.abortTransaction();
        }
        session.endSession();
    }
}

async function uploadAudio(req: Request, res: Response, next: NextFunction) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const preferLanguageHeader = req.headers['prefer-language'];
        const acceptsLanguages = req.acceptsLanguages();

        const language =
            (typeof preferLanguageHeader === 'string' ? preferLanguageHeader : preferLanguageHeader?.[0])?.split('_')[0] ??
            (typeof acceptsLanguages === 'string' ? acceptsLanguages : acceptsLanguages?.[0])?.split('-')[0];

        let prompt = '';
        if (req.acceptsLanguages().indexOf('zh-CN') != -1 || req.acceptsLanguages().indexOf('zh-Hans-CN') != -1) {
            prompt = '请转换为简体中文';
        }
        logger.debug('Prompt:', prompt);
        logger.debug('preferLanguage:', language);
        if (!req.file) {
            res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
            return;
        }

        const audioFilePath = req.file.path;
        const mimeType = mime.lookup(req.file.originalname);
        if (!mimeType) {
            throw new Error('Unrecognized file format');
        }
        const extension = mime.extension(mimeType);
        if (!extension) {
            throw new Error('Unrecognized file format');
        }

        // Rename the file with the correct extension
        const newFilePath = `${audioFilePath}.${extension}`;
        fs.renameSync(audioFilePath, newFilePath);

        // 使用 OpenAI Whisper 模型将音频转换为文本
        const transcription = await openai.audio.transcriptions.create({
            model: 'whisper-1',
            prompt: prompt,
            file: fs.createReadStream(newFilePath),
            response_format: 'text',
            language
        });

        // 删除临时音频文件
        fs.unlinkSync(newFilePath);
        logger.debug('Transcription:', transcription);

        // 直接返回转换后的文本结果
        // @ts-ignore
        if (!transcription || transcription == prompt || prompt.indexOf(transcription) != -1) {
            res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.InternalServerError, message: transcription ?? req.t('internal_server_error') });
        } else {
            res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.Ok, data: transcription });
        }
        await session.commitTransaction();
        session.endSession();
    } catch (e: any) {
        logger.error('Audio upload error:', e);
        await session.abortTransaction();
        if (e.code === 'invalid_language_format') {
            res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.BadRequest, message: req.t('un-suppored-langue') + ':\n' + e.message});
        } else {
            // @ts-ignore
            res.status(HttpStatusCode.Ok).send({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
        }
    } finally {
        if (session.inTransaction()) {
            await session.abortTransaction();
        }
        session.endSession();
    }
}

export { uploadAudioChat, uploadAudio };
