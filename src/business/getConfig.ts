import { Request, Response, NextFunction } from 'express';
import { Config } from 'src/models/Config';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';

export const getConfig = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  // 从请求头部获取语言
  const acceptLanguageHeader = req.headers['accept-language'];
  const lang = acceptLanguageHeader ? acceptLanguageHeader.split(',')[0].split('-')[0] : 'en'; // 默认为英文
  const appVersion = req.headers['app-version']

  try {
    const config = await Config.findOne({appVersion}).lean() || await Config.findOne({appVersion: '1.0'}).lean();
    if (config) {
      const configData = {
        appVersion: config.appVersion,
        privacyPolicy: config.privacyPolicy,
        termsOfService: config.termsOfService,
        supportEmail: config.supportEmail,
        featureFlags: config.featureFlags,
        compressRate: config.compressRate,
        // @ts-ignore
        promotLocal: config.promotLocal && lang in config.promotLocal ? config.promotLocal[lang] : null,
        // @ts-ignore
        promotCloud: config.promotCloud && lang in config.promotCloud ? config.promotCloud[lang] : null,
        // @ts-ignore
        mainSolgan: config.mainSolgan[lang] || config.mainSolgan['en'],
        // @ts-ignore
        registerSolgan: config.registerSolgan[lang] || config.registerSolgan['en'],
        // @ts-ignore
        emailLoginSolgan: config.emailLoginSolgan[lang] || config.emailLoginSolgan['en'],
        // @ts-ignore
        rechargeMessages: config.rechargeMessages[lang] || config.rechargeMessages['en'],
        // @ts-ignore
        hideMessage: config.hideMessage[lang] || config.hideMessage['en'],
        // @ts-ignore
        rechargeDescription: config.rechargeDescription[lang] || config.rechargeDescription['en'],
      };
      res.json({ code: HttpStatusCode.Ok, message: 'success', data: configData });
    } else {
      next({ code: HttpStatusCode.NotFound, message: req.t('data_not_found') });
    }
  } catch (error) {
    logger.debug('Error getting config:', error);
    next({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
  }
};

export const limit = async (req: Request, res: Response, next: NextFunction): Promise<void> => {

  // res.json({
  //   code: 10001,
  //   message: 'success',
  //   data: {
  //     msg: '',
  //     abValue: '',
  //     limit: false,
  //     subMsg: '～',
  //     extMsg: '',
  //     freeEquityStatus: false,
  //     type: 2,
  //     remainFreeCount: 0
  //   },
  //   success: true
  // });
  
}
