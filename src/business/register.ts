import { Request, Response, NextFunction } from 'express';
import User from 'src/models/User';
import { HttpStatusCode } from 'axios';

const register = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const newUser = new User(req.body);
        await newUser.save();
        res.status(HttpStatusCode.Ok).json({ message: req.t('register_success'), data: { username: newUser.username }, code: HttpStatusCode.Ok });
    } catch (error) {
        console.error(error);
        // @ts-ignore
        next({ message: `${req.t('register_fail')}: ${error.message}`, code: HttpStatusCode.InternalServerError, data: null });
    }
};

export default register;
