// 将 'your_database' 替换为你的数据库名称
const db = db.getSiblingDB('junshi');

// 脚本内容
const collections = db.getCollectionNames();

collections.forEach(collectionName => {
    print(`正在处理集合：${collectionName}`);
    // 删除字段 __v
    const result = db.getCollection(collectionName).updateMany(
        {},
        { $unset: { __v: 1 } }
    );
    print(`集合 ${collectionName} 更新结果：`, JSON.stringify(result));
});

print('所有集合处理完成');
