export const faqs = {
    en: {
        faqTitle: 'Frequently Asked Questions',
        faqs: [
            {
                question: 'What is Story Maker?',
                answer: 'Story Maker is an AI-powered interactive storytelling application that allows users to create unique stories by selecting different scenarios and steps. You can choose from a variety of preset scenes and options or input your own, letting Story Maker generate creative and personalized storylines based on your choices.'
            },
            {
                question: 'What can Story Maker do for me?',
                answer: '<ul><li><b>Create unique stories</b>: Whether it’s a fairytale, sci-fi, or mystery, Story Maker can generate storylines that fit the scenario based on your choices.</li><li><b>Engage in interactive adventures</b>: Throughout the story, you’ll face various scenarios and decisions, and each choice you make will affect the direction and outcome of the story.</li><li><b>Personalization options</b>: You can customize characters, set backgrounds, and define your story goals, allowing Story Maker to create a story that is uniquely yours.</li><li><b>Fun challenges and puzzles</b>: Story Maker isn’t just about narrative; it also offers fun interactions, puzzles, and challenges to add more engagement and fun to the experience.</li></ul>'
            },
            {
                question: 'How do I interact with AI in Story Maker?',
                answer: 'First, open the Story Maker app and choose the type of story you’re interested in. You can follow the preset scenarios for the next steps or input your own options. Based on your selections, the AI will generate the next part of the story, providing you with possible options for the next step. The progress of the story will be entirely shaped by your decisions, allowing you to enjoy a unique interactive experience.'
            },
            {
                question: 'How does Story Maker handle my personal information?',
                answer: 'Story Maker takes user privacy and data security very seriously. All the story choices you make and personal information you input are stored locally on your device and are never uploaded to the server. Please ensure that you safely store your data, as once lost, it cannot be recovered. For more information, please refer to our privacy policy.'
            },
            {
                question: 'What types of stories can I create with Story Maker?',
                answer: 'Story Maker supports various types of story creation, including but not limited to: fairytales, science fiction, mystery, romance, and adventure. Whether you want to create a heartwarming family story or an exciting mystery, Story Maker can generate scenarios based on your choices.'
            },
            {
                question: 'How do Story Maker’s interactive features enhance my experience?',
                answer: 'The uniqueness of Story Maker lies in its interactivity. Every story scenario changes based on the choices you make, and you can explore different story outcomes. Our AI doesn’t just generate text; it also provides you with fun challenges or puzzles at key moments to enhance the overall storytelling experience.'
            },
            {
                question: 'Can I create a story based on my own preferences?',
                answer: 'Yes! Story Maker allows you to customize the story content based on your preferences. You can set character names, backgrounds, and the overall tone of the story. Whether it’s a lighthearted story or a suspenseful adventure, Story Maker will generate creative content based on your settings.'
            },
            {
                question: 'What should I do if I encounter issues while using Story Maker?',
                answer: 'If you encounter any issues while using Story Maker, please contact our support team via the feedback feature in the app, or visit our official website for more help. We’ll provide assistance as soon as possible to ensure you have a smooth experience.'
            },
            {
                question: 'Is there a cost to using Story Maker?',
                answer: 'Story Maker offers a free basic story generation service, but some advanced features and storylines may require a fee to unlock. We use a pay-as-you-go model. For detailed pricing information, please check the pricing table within the app. Your support helps us continue improving and enhancing Story Maker’s services.'
            },
            {
                question: 'How do I configure my basic information in Story Maker?',
                answer: 'In the Story Maker settings, you can input your basic information, such as your name, the character’s gender, age, and the background story. This information will help Story Maker generate more personalized storylines. You can also skip these steps and directly jump into the story creation process if you prefer.'
            },
            {
                question: 'How does Story Maker generate storylines based on my choices?',
                answer: 'Story Maker uses advanced AI technology to dynamically generate storylines by combining the type of story you choose, character settings, and each step’s decision. Every story is unique, and it evolves based on your decisions, giving you a completely personalized experience.'
            },
            {
                question: 'Does Story Maker support multiple languages?',
                answer: 'Yes, Story Maker currently supports multiple languages, including English and Chinese. You can select your preferred language in the settings, and Story Maker will generate story content in the chosen language. In the future, we plan to support even more languages to cater to a global audience.'
            },
            {
                question: 'Are there any limitations on the stories Story Maker can generate?',
                answer: 'Currently, Story Maker mainly supports one-on-one interactive storytelling, and it’s not designed for multi-user or group story creation yet. However, we are working on future updates that will include support for multi-user interactive stories, so stay tuned!'
            }
        ]
    },
    zh: {
        faqTitle: '常见问题解答',
        faqs: [
            {
                question: '编故事是什么？',
                answer: '编故事是一款基于AI技术的互动故事生成应用，用户可以通过选择不同的故事情境和步骤，与AI一起编写出独特的故事。您可以从各种预设的情节和选项中进行选择，或者自行输入内容，让编故事为您生成具有创意且个性化的故事情节。'
            },
            {
                question: '编故事可以帮助我做什么？',
                answer: '<ul><li><b>创作独特的故事</b>：无论是童话、科幻还是悬疑，编故事可以根据您的选择生成符合情境的故事情节。</li><li><b>参与互动式冒险</b>：在故事中，您将面对各种情境和抉择，您的每个选择都会影响故事的发展和结局。</li><li><b>丰富的个性化选项</b>：您可以自定义角色、设定背景以及故事目标，让编故事根据这些信息生成完全属于您的故事。</li><li><b>趣味的挑战和解谜</b>：编故事不仅仅是单纯的叙事，还会为您提供有趣的互动、谜题和挑战，增加故事的趣味性和可玩性。</li></ul>'
            },
            {
                question: '如何使用编故事与AI进行互动？',
                answer: '首先，打开编故事应用，选择您感兴趣的故事类型。您可以在预设的情境中选择下一步，也可以自行输入选项。AI会根据您的选择，生成相应的故事发展情节，提供您下一步的可能选项。故事的进展将完全由您的决策来决定，让您享受独特的互动体验。'
            },
            {
                question: '编故事如何处理我的个人信息？',
                answer: '编故事非常重视用户的隐私和数据安全。所有您输入的故事选择和个人信息都存储在本地设备上，不会上传至服务器。请妥善保存您的数据，因为一旦丢失，将无法恢复。详见我们的隐私政策了解更多详细信息。'
            },
            {
                question: '编故事支持哪些类型的故事创作？',
                answer: '编故事支持多种类型的故事创作，包括但不限于：童话、科幻、悬疑、爱情、冒险等。无论您是想要编写温馨的家庭故事，还是刺激的悬疑故事，编故事都可以根据您的选择生成相应的情节。'
            },
            {
                question: '编故事的互动功能如何提升我的体验？',
                answer: '编故事的独特之处在于它的互动性。每个故事情节都会根据您的选择而变化，您可以选择探索不同的故事结局。我们的AI不仅仅是生成文本，还会在关键时刻为您提供有趣的挑战或谜题，增加故事的趣味性。'
            },
            {
                question: '我可以根据自己的喜好创建故事吗？',
                answer: '是的！编故事允许您根据自己的喜好定制故事内容。您可以设置角色的名字、背景以及故事的总体基调。无论是轻松愉快的故事，还是充满悬念的冒险，编故事都会根据您的设定进行创作。'
            },
            {
                question: '如何反馈我在使用编故事时遇到的问题？',
                answer: '如果您在使用编故事时遇到任何问题，请通过应用内的反馈功能联系我们的支持团队，或访问我们的官方网站获取更多帮助。我们将尽快为您提供支持，帮助您顺利体验编故事的所有功能。'
            },
            {
                question: '编故事的使用是否收费？',
                answer: '编故事目前提供免费的基本故事生成服务，但某些高级功能和情节可能需要付费解锁。我们采用按需收费的方式，具体收费详情请查看应用内的收费明细。您的支持将帮助我们不断优化和提升编故事的服务。'
            },
            {
                question: '如何配置编故事的基本信息？',
                answer: '在编故事的设置页面，您可以设置您的故事类型等。这些信息将有助于编故事生成更加个性化的情节。您也可以选择跳过这些步骤，直接进入故事创作环节。'
            },
            {
                question: '编故事如何根据我的选择生成情节？',
                answer: '编故事通过强大的AI技术，结合您选择的故事类型、角色设定和每一步的选择，生成动态的情节。每个故事都是独一无二的，并且会根据您的决策进行发展，带来完全个性化的体验。'
            },
            {
                question: '编故事可以支持多语言创作吗？',
                answer: '是的，目前编故事支持多语言创作，包括中文和英文。您可以在设置页面选择您偏好的语言，编故事将根据您的选择生成相应语言的故事内容。未来我们将支持更多语言，以便全球用户都能使用我们的服务。'
            },
            {
                question: '编故事的故事生成会受到限制吗？',
                answer: '编故事目前的功能主要支持1对1的互动式故事生成，不适用于多人或群体故事创作。但未来我们将会推出更多功能，包括多人互动故事的支持，敬请期待！'
            }
        ]
    }
};

export const contactUs = {
    en: {
        content: `<p>If you have any questions about these terms or our website, please contact <a href=\"mailto: <EMAIL>\"><EMAIL></a>.</p>`
    },
    zh: {
        content: `<p>如果您对这些条款或我们的网站有任何疑问，请联系我们：<a href=\"mailto: <EMAIL>\"><EMAIL></a>.</p>`
    },
    es: {
        content: `<p>Si tiene alguna pregunta sobre estos términos o nuestro sitio web, comuníquese con nosotros: <a href=\"mailto: <EMAIL>\"><EMAIL></a>.</p>`
    }
};

export const supportTitle = {
    en: 'Support',
    zh: '技术支持',
    es: 'Soporte'
};

export const exchangeContent = {
    en: 'The more you buy, the more cost-effective it is.\nNote: 1 USD ≈',
    zh: '购买越多越划算。注: 1 美元 ≈ ',
    es: 'Cuanto más compres, más rentable será.\nNota: 1 USD ≈ '
};

export const pricingInfo = {
    en: {
        pricingTitle: 'Pricing',
        alias: 'Alias',
        inPrice: 'In Price',
        outPrice: 'Out Price',
        count: 'Count',
        intro: 'Intro',
        faqTitle: 'Frequently Asked Questions (FAQ)',
        userGuideTitle: 'User Guide',
        contactUsTitle: 'Contact Us'
    },
    zh: {
        pricingTitle: '价格',
        alias: '别名',
        inPrice: '输入',
        outPrice: '输出',
        count: '字数',
        intro: '介绍',
        faqTitle: '常见问题 (FAQ)',
        userGuideTitle: '用户指南',
        contactUsTitle: '联系我们'
    },
    es: {
        pricingTitle: 'Precios',
        alias: 'Alias',
        inPrice: 'Precio de entrada',
        outPrice: 'Precio de salida',
        count: 'Contar',
        intro: 'Introducción',
        faqTitle: 'Preguntas Frecuentes (FAQ)',
        userGuideTitle: 'Guía del Usuario',
        contactUsTitle: 'Contáctenos'
    }
};

export const perI18n = {
    zh: '每 %d 个字符',
    en: 'per %d characters',
    es: 'por %d caracteres'
};
