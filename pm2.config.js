module.exports = {
    apps: [
        {
            name: 'text-game-release',
            script: 'dist/src/index.js',
            watch: false,
            autorestart: true,
            env: {
                NODE_ENV: 'production',
                OPENAI_API_KEY: '***************************************************',
                PORT: 53002
            },
            log_date_format: 'YYYY-MM-DD HH:mm Z',
            log_file: 'logs/release-info.log',
            error_file: 'logs/release-err.log',
            out_file: 'logs/release-out.log',
            merge_logs: true,
            instances: 1,
            exec_mode: 'fork',
            node_args: '-r ts-node/register -r tsconfig-paths/register',
        },
        {
            name: 'text-game-debug',
            script: 'dist/src/index.js',
            watch: true,
            autorestart: true,
            env: {
                NODE_ENV: 'debug',
                OPENAI_API_KEY: '***************************************************',
                PORT: 33002
            },
            ignore_watch: ['logs', 'dist', 'config.json', 'public/assets','uploads'],
            log_date_format: 'YYYY-MM-DD HH:mm Z',
            log_file: 'logs/debug-info.log',
            error_file: 'logs/debug-err.log',
            out_file: 'logs/debug-out.log',
            merge_logs: true,
            instances: 1,
            exec_mode: 'fork',
            node_args: '-r ts-node/register -r tsconfig-paths/register',
        }
    ]
};
